from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QApplication,
)

from PySide6.QtGui import QIcon
from PySide6.QtCore import (
    Qt,
    QPoint,
    QTimer,
)

from views.handlers.consulta_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from views.utils.widget_style import WidgetStyle
from views.utils.window_functions import (
    toggle_maximizar,
    toggle_minimize,
    mouseMoveEvent,
    mousePressEvent,
    mouseReleaseEvent,
    add_titlebar_widget,
    paintEvent,
    abrir_options,
    minimize_to_tray,
    quit_app,
)


class CustomWindow(QWidget):
    """
    Janela personalizada para a aplicação.
    """

    def __init__(self, parent=None):
        """
        Inicializa a janela personalizada.
        """
        super().__init__(parent)

        # Inicializa a lista de widgets extras da barra de título
        self.extra_titlebar_widgets = []

        # Inicializa o estado da janela
        self._was_maximized = False

        # Instala um filtro de eventos para capturar eventos nativos do Windows
        self.installEventFilter(self)

        # Dá handle no scaling de DPI
        self.handle_dpi_scaling()

        # Setup da detecção de mudança de tela
        self.setup_screen_change_detection()

    def handle_dpi_scaling(self):
        """
        Configura a aplicação para lidar com diferentes escalas de DPI.
        Usando uma abordagem simplificada para evitar problemas de geometria.
        """
        # Define um tamanho min
        self.setMinimumSize(800, 600)

    def setup_screen_change_detection(self):
        """
        Configura a detecção de mudança de tela para ajustar a janela quando necessário.
        """
        # Guarda a referência da tela atual
        self.current_screen = QApplication.primaryScreen()

        # Cria um timer para verificar periodicamente se a tela mudou
        self.screen_check_timer = QTimer(self)
        self.screen_check_timer.timeout.connect(self.check_screen_change)
        self.screen_check_timer.start(1000)

    def check_screen_change(self):
        """
        Verifica se a janela mudou de tela e ajusta se necessário.
        """
        if not self.isVisible():
            return

        # Pega a tela atual
        screen = QApplication.screenAt(self.mapToGlobal(QPoint(self.width()//2, self.height()//2)))

        # Se a tela mudou, ajusta ela
        if screen and screen != self.current_screen:
            self.handle_screen_change(screen)

    def handle_screen_change(self, screen=None):
        """
        Manipula a mudança de tela ajustando a janela conforme necessário.
        """
        try:
            # Se não haver nenhuma tela a mais, põe a janela na tela atual
            if not screen:
                screen = QApplication.screenAt(self.mapToGlobal(QPoint(self.width()//2, self.height()//2)))
                if not screen:
                    return

            # Dá att na variável da tela atual
            self.current_screen = screen

        except Exception:
            return

    def eventFilter(self, obj, event):
        """
        Filtra eventos para capturar eventos específicos do Windows.
        """
        from PySide6.QtCore import QEvent

        # Captura eventos de ativação da janela (incluindo cliques na barra de tarefas)
        if event.type() == QEvent.WindowActivate:
            # Se a janela estava minimizada, restaura para o estado anterior
            if self.isMinimized():
                # Restaura para o estado anterior (maximizado ou normal)
                if hasattr(self, '_was_maximized') and self._was_maximized:
                    # Atualiza o texto do botão de maximizar
                    if hasattr(self, 'btn_maximizar'):
                        self.btn_maximizar.setText("❐")
                    # Maximiza a janela após um pequeno delay
                    QTimer.singleShot(50, self.showMaximized)
                else:
                    # Atualiza o texto do botão de maximizar
                    if hasattr(self, 'btn_maximizar'):
                        self.btn_maximizar.setText("⬜")
                    # Normaliza a janela após um pequeno delay
                    QTimer.singleShot(50, self.showNormal)

                # Ativa a janela para trazer para frente
                QTimer.singleShot(100, self.activateWindow)
                QTimer.singleShot(100, self.raise_)

        # Captura mudanças de estado da janela
        elif event.type() == QEvent.WindowStateChange:
            # Armazena o estado maximizado atual para uso futuro
            self._was_maximized = bool(self.windowState() & Qt.WindowMaximized)

            # Atualiza o texto do botão de maximizar
            if hasattr(self, 'btn_maximizar'):
                if self.isMaximized():
                    self.btn_maximizar.setText("❐")
                else:
                    self.btn_maximizar.setText("⬜")

        # Passa o evento para o processamento padrão
        return super().eventFilter(obj, event)

    def changeEvent(self, event):
        """
        Manipula eventos de mudança de estado da janela.
        """
        # Passa o evento para a implementação padrão
        super().changeEvent(event)

    def init_ui(self):
        """
        Inicializa a interface da janela customizada.
        """
        # Chamando as functions de botões
        self.paintEvent = paintEvent.__get__(self)
        self.mousePressEvent = mousePressEvent.__get__(self)
        self.mouseMoveEvent = mouseMoveEvent.__get__(self)
        self.mouseReleaseEvent = mouseReleaseEvent.__get__(self)
        self.add_titlebar_widget = add_titlebar_widget.__get__(self)
        self.toggle_maximizar = toggle_maximizar.__get__(self)
        self.toggle_minimize = toggle_minimize.__get__(self)
        self.abrir_options = abrir_options.__get__(self)
        self.minimize_to_tray = minimize_to_tray.__get__(self)
        self.quit_app = quit_app.__get__(self)

        # Configurações da janela
        #self.setWindowTitle("Pratiko - ES Logistics")
        self.setWindowFlags(Qt.Window)
        self.setAttribute(Qt.WA_TranslucentBackground)
        self.setWindowIcon(QIcon(ConsultaHandler.resolve_path("assets/avatar_ES.ico")))

        # Estilo dos widgets
        style = WidgetStyle()

        # Atributos para arrastar a janela
        self.dragging = False
        self.offset = QPoint()

        # Layout principal
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(15)

        #Setup da barra de título
        titulo_layout = QHBoxLayout()
        titulo_layout.setContentsMargins(0, 0, 0, 0)
        titulo_layout.setSpacing(5)

        # Adicionando o estilo
        self.setStyleSheet(style.style_sheet)

        # Setup do botão de opções
        btn_options = QPushButton("")
        btn_options.setIcon(QIcon(ConsultaHandler.resolve_path("assets/gear.png")))
        btn_options.setFixedSize(30, 30)
        btn_options.setStyleSheet(style.buttons_default)
        btn_options.clicked.connect(self.abrir_options)

        # Setup do botão de minimizar
        self.btn_minimizar = QPushButton("-")
        self.btn_minimizar.setFixedSize(30, 30)
        self.btn_minimizar.setStyleSheet(style.buttons_default)
        self.btn_minimizar.clicked.connect(lambda: self.toggle_minimize())

        # Setup do botão de maximizar
        self.btn_maximizar = QPushButton("⬜")
        self.btn_maximizar.setFixedSize(30, 30)
        self.btn_maximizar.setStyleSheet(style.buttons_default)
        self.btn_maximizar.clicked.connect(lambda: self.toggle_maximizar())

        # Setup do botão de fechar
        btn_fechar = QPushButton("X")
        btn_fechar.setFixedSize(30, 30)
        btn_fechar.setStyleSheet(style.button_close)
        btn_fechar.clicked.connect(self.minimize_to_tray)

        # Adicionando os botões à barra de título
        titulo_layout.addWidget(btn_options)
        titulo_layout.addStretch() # Espaço à esquerda
        titulo_layout.addWidget(self.btn_minimizar)
        titulo_layout.addWidget(self.btn_maximizar)
        titulo_layout.addWidget(btn_fechar)

        layout.addLayout(titulo_layout)
        self.setLayout(layout)
