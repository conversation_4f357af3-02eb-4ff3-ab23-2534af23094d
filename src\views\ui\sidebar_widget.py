from PySide6.QtWidgets import (
    QW<PERSON>t,
    QVBoxLayout,
    QHBoxLayout,
    QPushButton,
    QLabel,
    QFrame,
    QSizePolicy,
    QToolButton,
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QIcon, QPixmap, QFont

from views.handlers.consulta_handler import ConsultaHandler
from views.utils.widget_style import WidgetStyle
from services.language_utils.translations_functions import translate as t


class SidebarWidget(QWidget):
    """
    Widget da barra lateral da aplicação.
    """

    # Sinais para comunicação com a janela principal
    scheduler_requested = Signal()
    settings_requested = Signal()
    about_requested = Signal()

    def __init__(self, parent=None):
        """
        Inicializa o widget da sidebar.

        Args:
            parent: Widget pai
        """
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """
        Inicializa a interface da sidebar.
        """
        # Layout principal da sidebar
        layout = QVBoxLayout()
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        layout.setAlignment(Qt.AlignTop)

        # Logo da aplicação (menor para a sidebar)
        logo_path = ConsultaHandler.resolve_path("assets/Pratiko_Logo.png")
        if logo_path:
            logo_label = QLabel()
            pixmap = QPixmap(logo_path)
            logo_label.setPixmap(
                pixmap.scaled(120, 60, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            )
            logo_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(logo_label)

        # Separador
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # Botões de navegação
        self.create_navigation_buttons(layout)

        # Espaçador para empurrar tudo para cima
        layout.addStretch()

        # Logo da empresa (menor)
        empresa_logo_path = ConsultaHandler.resolve_path("assets/ES_Logo.png")
        if empresa_logo_path:
            empresa_logo_label = QLabel()
            pixmap = QPixmap(empresa_logo_path)
            empresa_logo_label.setPixmap(
                pixmap.scaled(80, 40, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            )
            empresa_logo_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(empresa_logo_label)

        self.setLayout(layout)

        # Define tamanho fixo para a sidebar
        self.setFixedWidth(200)
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)

    def create_navigation_buttons(self, layout):
        """
        Cria os botões de navegação da sidebar.

        Args:
            layout: Layout onde adicionar os botões
        """
        # Estilo dos botões
        style = WidgetStyle()

        # Botão do Scheduler
        btn_scheduler = QPushButton(t("Agendador"))
        btn_scheduler.setIcon(QIcon(ConsultaHandler.resolve_path("assets/gear.png")))
        btn_scheduler.clicked.connect(self.scheduler_requested.emit)
        btn_scheduler.setStyleSheet(style.buttons_default)
        btn_scheduler.setMinimumHeight(40)
        layout.addWidget(btn_scheduler)

        # Botão de Configurações
        btn_settings = QPushButton(t("Configurações"))
        btn_settings.setIcon(QIcon(ConsultaHandler.resolve_path("assets/gear.png")))
        btn_settings.clicked.connect(self.settings_requested.emit)
        btn_settings.setStyleSheet(style.buttons_default)
        btn_settings.setMinimumHeight(40)
        layout.addWidget(btn_settings)

        # Botão Sobre
        btn_about = QPushButton(t("Sobre"))
        btn_about.setIcon(QIcon(ConsultaHandler.resolve_path("assets/gear.png")))
        btn_about.clicked.connect(self.about_requested.emit)
        btn_about.setStyleSheet(style.buttons_default)
        btn_about.setMinimumHeight(40)
        layout.addWidget(btn_about)
