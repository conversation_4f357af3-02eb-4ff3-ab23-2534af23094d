import os

from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QLabel,
    QSizePolicy,
    QToolButton,
)
from PySide6.QtCore import (
    Qt,
    Signal,
    QSize
)
from PySide6.QtGui import (
    QIcon,
    QPixmap,
    QFont
)
from views.handlers.consulta_handler import ConsultaHandler
from views.utils.widget_style import WidgetStyle
from services.language_utils.translations_functions import translate as t


class SidebarWidget(QWidget):
    """
    Widget da barra lateral da aplicação.
    """

    # Sinais para comunicação com a janela principal
    scheduler_requested = Signal()
    settings_requested = Signal()
    about_requested = Signal()

    def __init__(self, parent=None):
        """
        Inicializa o widget da sidebar.

        Args:
            parent: Widget pai
        """
        super().__init__(parent)
        self.init_ui()

    def init_ui(self):
        """
        Inicializa a interface da sidebar.
        """
        # Layout principal da sidebar
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 10, 5, 10)
        layout.setSpacing(0)
        layout.setAlignment(Qt.AlignTop)

        # Logo removido da sidebar - agora será na barra de título

        # Botões de navegação estilo Teams
        self.create_teams_style_buttons(layout)

        # Espaçador para empurrar tudo para cima
        layout.addStretch()

        self.setLayout(layout)

        # Define tamanho fixo para a sidebar (mais estreita como Teams)
        self.setFixedWidth(80)
        self.setSizePolicy(QSizePolicy.Fixed, QSizePolicy.Expanding)

    def create_teams_style_buttons(self, layout):
        """
        Cria os botões de navegação estilo Microsoft Teams.

        Args:
            layout: Layout onde adicionar os botões
        """
        # Estilo dos botões
        style = WidgetStyle()

        # Estilo personalizado para botões estilo Teams (mantendo suas cores)
        teams_button_style = f"""
            QToolButton {{
                {style.buttons_default.replace('QPushButton', 'QToolButton')}
                border: none;
                border-radius: 8px;
                padding: 8px;
                margin: 2px;
                text-align: center;
                font-size: 10px;
                min-width: 60px;
                max-width: 60px;
                min-height: 60px;
                max-height: 60px;
            }}
            QToolButton:hover {{
                background-color: rgba(255, 255, 255, 0.1);
            }}
            QToolButton:pressed {{
                background-color: rgba(255, 255, 255, 0.2);
            }}
        """

        # Botão do Scheduler
        btn_scheduler = self.create_teams_button(
            icon_path="/../../assets/gear.png",
            text=t("Agendador"),
            callback=self.scheduler_requested.emit,
            style=teams_button_style
        )
        layout.addWidget(btn_scheduler)
        layout.addSpacing(5)

        # Botão de Configurações
        btn_settings = self.create_teams_button(
            icon_path="/../../assets/gear.png",
            text=t("Config"),
            callback=self.settings_requested.emit,
            style=teams_button_style
        )
        layout.addWidget(btn_settings)
        layout.addSpacing(5)

        # Botão Sobre
        btn_about = self.create_teams_button(
            icon_path="/../../assets/gear.png",
            text=t("Sobre"),
            callback=self.about_requested.emit,
            style=teams_button_style
        )
        layout.addWidget(btn_about)

    def create_teams_button(self, icon_path, text, callback, style):
        """
        Cria um botão individual estilo Teams.

        Args:
            icon_path: Caminho para o ícone
            text: Texto do botão
            callback: Função a ser chamada no clique
            style: Estilo CSS do botão

        Returns:
            QToolButton: Botão configurado
        """
        button = QToolButton()

        # Configurar ícone
        icon = QIcon(ConsultaHandler.resolve_path(icon_path))
        button.setIcon(icon)
        button.setIconSize(QSize(24, 24))

        # Configurar texto
        button.setText(text)
        button.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)

        # Configurar fonte menor
        font = QFont()
        font.setPointSize(8)
        button.setFont(font)

        # Aplicar estilo
        button.setStyleSheet(style)

        # Conectar callback
        button.clicked.connect(callback)

        return button
