import json
import os

from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap
from PySide6.QtWidgets import (
    QLabel,
    QPushButton,
    QComboBox,
    QGridLayout,
    QMessageBox,
    QDockWidget,
)

from controllers.main_controller import Main<PERSON>ontroller
from views.form_inputs import FormInputs
from views.data_table import DataTable
from views.custom_window import CustomWindow
from views.utils.widget_style import WidgetStyle
from views.handlers.consulta_handler import Consul<PERSON><PERSON>andler
from services.scheduler_service import SchedulerService
from views.ui.process_filter_widget import ProcessFilterWidget
from views.ui.window_animations import WindowAnimations
from views.ui.sidebar_widget import SidebarWidget

from services.language_utils.translations_functions import (
    get_system_language,
    translate as t,
)
from views.handlers.file_handler import (
    <PERSON><PERSON>and<PERSON>,
    salvar_excel
)
from views.utils.front_services import (
    exibir_erro,
    exibir_information,
    exibir_question,
)


class PratikoApp(CustomWindow):
    """
    Classe principal da aplicação Pratiko, que herda de CustomWindow.
    """

    def __init__(self, api_keys_path, parent=None):
        """
        Inicializa a janela principal da aplicação.

        Args:
            api_keys_path (str): Caminho para o arquivo JSON que contém as chaves de API.
            parent: (QWidget, optional): Janela pai, se houver. Default to None.
        """
        super().__init__(parent)
        self.api_keys_path = api_keys_path
        self.api_keys = self.load_api_keys()  # Carrega as API Keys do arquivo JSON
        self.current_language = get_system_language()
        self.label_status = QLabel()  # Label para exibir o status da API Key
        self.current_status_key = ""  # Armazena a chave do status atual para traduzir dps
        self.current_status_params = {}  # Armazena os parâmetros do status atual para tradução
        self.scheduler_service = None  # Inicializa o serviço de agendamento (scheduler_service)
        self.controller = None
        self.api_key = None
        self.df = None
        self.init_ui()

    def showEvent(self, event):
        if not hasattr(self, '_animated'):
            super().showEvent(event)
            self.setWindowOpacity(0.0)  # Opacidade inicial
            WindowAnimations.animate_window_open(self)
            self._animated = True
        if hasattr(self, '_animated'):
            super().showEvent(event)

    def init_ui(self):
        """
        Inicializa a interface do usuário.
        """
        super().init_ui()

        # Configurar a sidebar
        self.setup_sidebar()

        layout = self.centralWidget().layout()

        # Logo removido do conteúdo principal - agora está na barra de título

        self.label_intro = QLabel(
            t(
                "<p>Bem-vindo ao <b>Pratiko</b>, " \
                "seu assistente de dados da <b>ES Logistics</b>.</p>"
            )
        )

        # Alinhamento do texto no centro da Interface
        self.label_intro.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label_intro)

        # Alinhamento do status no centro da Interface
        self.label_status.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.label_status)

        # Este é o espaço reservado para widgets ou funcionalidades adicionais.
        self.form_inputs = FormInputs(self)
        layout.addWidget(self.form_inputs)

        # Widget para seleção do tipo de processo (movido das configurações)
        self.process_filter = ProcessFilterWidget(
            self,
            selected_filter=getattr(self, "selected_filter", None),
            current_language=self.current_language
        )
        self.process_filter.filter_changed.connect(self.on_filter_changed)
        layout.addWidget(self.process_filter)

        # Selecionar múltiplas API Keys
        self.api_key_selector = QComboBox(self)
        self.api_key_selector.addItems(self.api_keys.keys())
        self.api_key_selector.currentIndexChanged.connect(self.change_api_key)
        # Adiciona borda ao seletor de API key
        self.api_key_selector.setStyleSheet(WidgetStyle.api_key_style())
        layout.addWidget(self.api_key_selector)

        # Selecionar a primeira API disponível automaticamente
        if self.api_keys:
            first_key = list(self.api_keys.keys())[0]  # Pega a primeira chave disponível
            self.api_key_selector.setCurrentText(first_key)
            self.api_key = self.api_keys[first_key]
            self.controller = MainController(api_key=self.api_key, parent=self)

            # Armazena a chave e parâmetros do status para tradução posterior
            self.current_status_key = '<b>Status:</b> API Key "{first_key}" selecionada automaticamente.'
            self.current_status_params = {"first_key": first_key}

            # Define o texto do status
            self.label_status.setText(t(
                self.current_status_key
                ).format(first_key=first_key)
            )

            if self.scheduler_service is None:
                self.scheduler_service = SchedulerService(
                    api_key=self.api_key
                )

            # Salva a empresa selecionada
            self.scheduler_service.selected_company = first_key
            config = self.scheduler_service.load_schedules() or {}
            config["selected_company"] = first_key
            with open(self.scheduler_service.config_path, "w", encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)

        # Botões em linhas separadas
        button_layout = QGridLayout()

        # Botão de incluir nova API Key
        self.btn_incluir_api_key = QPushButton(t("Adicionar Chave"))
        self.btn_incluir_api_key.clicked.connect(self.add_api_key)
        button_layout.addWidget(self.btn_incluir_api_key, 1, 0)

        # Botão de consulta
        self.btn_consulta = QPushButton(t("Buscar Dados"))
        self.btn_consulta.clicked.connect(self.executar_consulta)
        button_layout.addWidget(self.btn_consulta, 0, 0)

        # Botão de salvar dados em Excel
        self.btn_salvar = QPushButton(t("Salvar Excel"))
        self.btn_salvar.clicked.connect(self.salvar_excel)
        self.btn_salvar.setEnabled(False)
        button_layout.addWidget(self.btn_salvar, 0, 1)

        # Botão de excluir API Key
        self.btn_excluir_api_key = QPushButton(t("Excluir Chave"))
        self.btn_excluir_api_key.clicked.connect(self.excluir_api_key_selecionada)
        button_layout.addWidget(self.btn_excluir_api_key, 1, 1)

        # Inicializa a tabela de dados e adiciona ao layout
        self.tabela = DataTable(self)
        layout.addWidget(self.tabela)
        layout.addLayout(button_layout)

        # Adiciona a logo da empresa no canto inferior direito
        empresa_logo_path = ConsultaHandler.resolve_path("assets/ES_Logo.png")
        empresa_logo_label = QLabel(self)
        if os.path.exists(empresa_logo_path):
            pixmap = QPixmap(empresa_logo_path)
            empresa_logo_label.setPixmap(
                pixmap.scaled(100, 50, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            )
            empresa_logo_label.setAlignment(Qt.AlignRight | Qt.AlignBottom)
            layout.addWidget(empresa_logo_label)
        else:
            exibir_information(self, t("Arquivo da logo da empresa não encontrado."))

        # Maximiza a janela
        QTimer.singleShot(100, self.showMaximized)

    def setup_sidebar(self):
        """
        Configura a sidebar da aplicação.
        """
        # Criar o widget da sidebar
        self.sidebar_widget = SidebarWidget(self)

        # Criar o dock widget
        self.sidebar_dock = QDockWidget(t("Navegação"), self)
        self.sidebar_dock.setWidget(self.sidebar_widget)
        self.sidebar_dock.setFeatures(QDockWidget.DockWidgetMovable | QDockWidget.DockWidgetFloatable)

        # Adicionar o dock widget à esquerda
        self.addDockWidget(Qt.LeftDockWidgetArea, self.sidebar_dock)

        # Conectar os sinais da sidebar
        self.sidebar_widget.scheduler_requested.connect(self.open_scheduler)
        self.sidebar_widget.settings_requested.connect(self.abrir_options)
        self.sidebar_widget.about_requested.connect(self.show_about)

    def load_api_keys(self):
        """
        Chama a função de carregar as API Keys
        """
        if os.path.exists(self.api_keys_path):
            try:
                with open(self.api_keys_path, "r") as file:
                    return json.load(file)
            except Exception as e:
                exibir_erro(self, t(f"Erro ao carregar as API Keys: {e}"))
                return {}
        else:
            return {}

    def save_api_keys(self):
        try:
            os.makedirs(os.path.dirname(self.api_keys_path), exist_ok=True)
            with open(self.api_keys_path, "w") as file:
                json.dump(self.api_keys, file, indent=4)
        except Exception as e:
            exibir_erro(self, t(f"Erro ao salvar as API Keys: {e}"))

    def change_api_key(self):
        """
        Chama a função de alterar a API Key
        """
        selected_name = self.api_key_selector.currentText()
        if selected_name in self.api_keys:
            self.api_key = self.api_keys[selected_name]
            self.controller = MainController(api_key=self.api_key, parent=self)

            # Armazena a chave e parâmetros do status para tradução posterior
            self.current_status_key = '<b>Status:</b> API Key "{selected_name}" selecionada com sucesso.'
            self.current_status_params = {"selected_name": selected_name}

            # Define o texto do status
            self.label_status.setText(
                t(self.current_status_key).format(selected_name=selected_name)
            )

            if hasattr(self, "scheduler_service"):
                self.scheduler_service.selected_company = selected_name
                config = self.scheduler_service.load_schedules() or {}
                config["selected_company"] = selected_name
                with open(self.scheduler_service.config_path, "w", encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)

        else:
            # Armazena a chave do status para tradução posterior
            self.current_status_key = "<b>Status:</b> API Key não encontrada. Selecione uma válida."
            self.current_status_params = {}

            # Define o texto do status
            self.label_status.setText(
                t(self.current_status_key)
            )

    def add_api_key(self):
        """
        Chama a função de incluir nova API Key
        """
        new_api_keys = FileHandler.incluir_api_keys(self.api_keys_path, self)
        if new_api_keys:
            self.api_keys = new_api_keys
            self.api_key_selector.clear()
            self.api_key_selector.addItems(self.api_keys.keys())

    def executar_consulta(self):
        """
        Chama a função de consulta com os parâmetros fornecidos pelo usuário
        """
        if not getattr(self, "api_key", None):
            exibir_erro(self, t("Insira uma chave de API para continuar."))
            return

        self.tabela.setRowCount(0)
        self.tabela.setColumnCount(0)

        consulta = ConsultaHandler(
            self.controller,
            self.form_inputs,
            self.tabela,
            self.btn_salvar,
            self
        )
        self.df = consulta.executar_consulta()

    def excluir_api_key_selecionada(self):
        """
        Chama a função de excluir API Key
        """
        selected_name = self.api_key_selector.currentText()

        if not selected_name:
            exibir_erro(self, t("Nenhuma chave de API selecionada."))
            return

        confirm = exibir_question(
            self,
            t('Tem certeza de que deseja excluir a chave de API "{selected_name}"?'
              ).format(selected_name=selected_name),
        )

        if confirm == QMessageBox.Yes:
            updated_api_keys = FileHandler.excluir_api_key_selecionada(
                self.api_keys_path,
                selected_name, self
            )

            if updated_api_keys is not None:
                self.api_keys = updated_api_keys
                current_index = self.api_key_selector.currentIndex()

                self.api_key_selector.clear()
                self.api_key_selector.addItems(self.api_keys.keys())

                if self.api_keys:
                    current_index = min(self.api_key_selector.currentIndex(), self.api_key_selector.count() - 1)
                    self.api_key_selector.setCurrentIndex(current_index)
                    self.change_api_key()
                else:
                    self.api_key = None
                    self.controller = None

                    # Armazena a chave do status para tradução posterior
                    self.current_status_key = "<b>Status:</b> Nenhuma chave de API disponível."
                    self.current_status_params = {}

                    # Define o texto do status
                    self.label_status.setText(t(self.current_status_key))
            else:
                exibir_erro(self, t("Erro ao excluir a chave de API."))

    def salvar_excel(self):
        """
        Chama a função de salvar dados em Excel
        """
        salvar_excel(self.df, self)

    def update_scheduler_references(self):
        """
        Atualiza as referências do scheduler service após o tray_icon estar disponível
        """
        if hasattr(self, 'scheduler_service') and self.scheduler_service is not None:
            self.scheduler_service.tray_icon = self.tray_icon
            self.scheduler_service.main_window = self

    def save_selected_columns(self):
        """
        Salva as colunas selecionadas para o CLI.
        """
        try:
            # Cria o diretório de configuração se não existir
            config_dir = os.path.dirname(os.path.dirname(__file__))
            columns_path = os.path.join(config_dir, 'columns.json')

            # Cria o arquivo de configuração se não existir
            config = {
                'selected_columns': self.selected_columns,
                'selected_filter': self.selected_filter
            }

            # Salva no arquivo
            with open(columns_path, 'w') as f:
                json.dump(config, f, indent=2)
        except:
            pass

    def on_filter_changed(self, filter_text):
        """
        Manipula a alteração do filtro de processos.

        Args:
            filter_text (str): Texto do filtro selecionado.
        """
        self.selected_filter = filter_text

        # Se houver dados na tabela, aplica o filtro
        if hasattr(self, 'df') and self.df is not None:
            consulta = ConsultaHandler(
                self.controller,
                self.form_inputs,
                self.tabela,
                self.btn_salvar,
                self
            )
            self.df = consulta.executar_consulta()

        # Salva a configuração
        self.save_selected_columns()

    def open_scheduler(self):
        """
        Abre o diálogo do agendador.
        """
        # Importa aqui para evitar importação circular
        from views.ui.scheduler.scheduler_dialog import SchedulerDialog

        if not hasattr(self, 'scheduler_service') or self.scheduler_service is None:
            if self.api_key:
                self.scheduler_service = SchedulerService(api_key=self.api_key)
            else:
                from views.utils.front_services import exibir_erro
                exibir_erro(self, t("Selecione uma API Key antes de acessar o agendador."))
                return

        dialog = SchedulerDialog(self, self.scheduler_service)
        dialog.exec()

    def show_about(self):
        """
        Mostra informações sobre a aplicação.
        """
        from views.utils.front_services import exibir_information
        about_text = t(
            "<h3>Pratiko - ES Logistics</h3>"
            "<p>Assistente de dados para ES Logistics</p>"
            "<p>Versão: 1.0</p>"
            "<p>Desenvolvido com PySide6</p>"
        )
        exibir_information(self, about_text)
